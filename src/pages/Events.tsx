import { motion } from 'framer-motion';
import { FaCalendarAlt, FaMapMarkerAlt, FaClock } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { useState } from 'react';

const Events = () => {
  const [activeCategory, setActiveCategory] = useState('All Events');

  const events = [
    {
      id: 1,
      title: 'Alumni Mix & Mingle',
      date: 'Saturday, March 23, 2024',
      time: '6:00 PM - 9:00 PM',
      location: 'Palm Beach Lakes High School',
      description: 'Join us for an evening of networking, reminiscing, and celebrating our shared legacy. Connect with fellow alumni, enjoy refreshments, and help support our ongoing initiatives.',
      image: '/images/events/mix-mingle.jpeg',
      categories: ['Networking', 'Social']
    }
  ];

  const filteredEvents = events.filter(event => {
    const matchesCategory = 
      activeCategory === 'All Events' ||
      event.categories.includes(activeCategory);

    return matchesCategory;
  });

  const categories = ['All Events', 'Reunions', 'Fundraisers', 'Networking', 'Community'];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-gray-50"
    >
      {/* Hero Section with Decorative Elements */}
      <div className="relative overflow-hidden bg-gradient-to-r from-[#7D0E17] to-[#590A11] text-white">
        <div className="absolute inset-0">
          <div className="absolute right-0 top-0 w-1/2 h-full bg-white/5 transform skew-x-12"></div>
          <div className="absolute left-0 bottom-0 w-1/3 h-2/3 bg-black/10 transform -skew-x-12"></div>
          <div className="absolute right-1/4 top-1/4 w-48 h-48 bg-white/10 rounded-full blur-2xl"></div>
          <div className="absolute left-1/4 bottom-1/4 w-64 h-64 bg-black/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative container-custom py-24">
          <div className="max-w-4xl mx-auto text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-5xl md:text-6xl font-bold mb-8"
            >
              Events
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-gray-200 leading-relaxed mb-12"
            >
              Connect with fellow alumni and support our school community through these upcoming gatherings
            </motion.p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-16 container-custom">
        <div className="max-w-7xl mx-auto">
          {/* Event Categories */}
          <div className="flex flex-wrap gap-4 justify-center mb-12">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-6 py-2 rounded-full border transition-all duration-300 shadow-sm ${
                  activeCategory === category
                    ? 'bg-[#7D0E17] text-white border-transparent'
                    : 'bg-white border-gray-200 text-gray-700 hover:bg-[#7D0E17] hover:text-white hover:border-transparent'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {filteredEvents.length > 0 ? (
            <>
              {/* Featured Events */}
              {filteredEvents.map(event => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="mb-16"
                >
                  <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
                    <div className="grid md:grid-cols-2 gap-0">
                      <div className="relative h-[400px] md:h-[600px]">
                        <img 
                          src={event.image}
                          alt={event.title}
                          className="absolute inset-0 w-full h-full object-contain bg-gray-100"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      </div>
                      <div className="p-8 md:p-12">
                        <span className="inline-block px-4 py-1 bg-[#7D0E17]/10 text-[#7D0E17] rounded-full text-sm font-semibold mb-4">
                          Featured Event
                        </span>
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">{event.title}</h2>
                        <div className="flex items-center text-gray-600 mb-4">
                          <FaCalendarAlt className="mr-2" />
                          <span>{event.date}</span>
                        </div>
                        <div className="flex items-center text-gray-600 mb-4">
                          <FaClock className="mr-2" />
                          <span>{event.time}</span>
                        </div>
                        <div className="flex items-center text-gray-600 mb-6">
                          <FaMapMarkerAlt className="mr-2" />
                          <span>{event.location}</span>
                        </div>
                        <p className="text-gray-600 mb-8">{event.description}</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </>
          ) : (
            /* No Events Found State */
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center py-16"
            >
              <div className="max-w-md mx-auto">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FaCalendarAlt className="text-3xl text-gray-400" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">No Events Found</h2>
                <p className="text-gray-600 mb-8">
                  Stay tuned for more upcoming events and reunions. We're constantly adding new opportunities to connect with your fellow alumni.
                </p>
                <div>
                  <Link 
                    to="/contact"
                    className="inline-block w-full px-6 py-3 bg-[#7D0E17] text-white rounded-lg hover:bg-[#6B0C14] transition-colors shadow-lg hover:shadow-xl"
                  >
                    Suggest an Event
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default Events; 