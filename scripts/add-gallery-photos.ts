import { galleryApi } from '../src/lib/api';

async function addGalleryPhotos() {
  try {
    const items = [
      {
        title: "Scholarship Award Ceremony",
        description: "PBLHS Alumni Association scholarship award ceremony",
        category: "achievements",
        year: "2025",
        image_url: "/images/gallery/scholarship-award.jpg",
        is_featured: true,
        sort_order: 1
      },
      {
        title: "Scholarship Recipients",
        description: "Proud recipients of the PBLHS Alumni Association scholarships",
        category: "achievements",
        year: "2025",
        image_url: "/images/gallery/scholarship.jpg",
        is_featured: false,
        sort_order: 2
      },
      {
        title: "<PERSON><PERSON>",
        description: "Notable PBLHS alumni <PERSON><PERSON> during his athletic career",
        category: "sports",
        year: "2025",
        image_url: "/images/gallery/vinny-sutherland.jpeg",
        is_featured: true,
        sort_order: 3
      },
      {
        title: "Alumni Event Photo 1",
        description: "Memorable moments from our alumni gatherings",
        category: "events",
        year: "2025",
        image_url: "/images/gallery/photo1.jpg",
        is_featured: false,
        sort_order: 4
      },
      {
        title: "Alumni Event Photo 2",
        description: "PBLHS alumni coming together to celebrate our shared history",
        category: "events",
        year: "2025",
        image_url: "/images/gallery/photo2.jpg",
        is_featured: false,
        sort_order: 5
      }
    ];

    console.log('Adding gallery photos...');
    const result = await galleryApi.batchCreate(items);
    console.log('Successfully added', result.length, 'photos to the gallery!');
  } catch (error) {
    console.error('Error adding gallery photos:', error);
  }
}

addGalleryPhotos(); 