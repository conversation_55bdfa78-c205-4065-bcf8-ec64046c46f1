import { motion } from 'framer-motion';
import { FaPhone, FaEnvelope, FaFacebookF, FaTwitter, FaInstagram, FaLinkedinIn, FaPaperPlane, FaClock, FaHeart } from 'react-icons/fa';

const Contact = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen relative overflow-hidden"
    >
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 right-10 w-72 h-72 bg-gradient-to-br from-crimson-100/40 to-beige-100/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-10 w-96 h-96 bg-gradient-to-tr from-gold-100/30 to-crimson-50/40 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-beige-50/20 to-transparent rounded-full blur-2xl"></div>
      </div>

      {/* Main content */}
      <motion.div 
        className="py-20 container-custom relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <motion.div 
            className="text-center mb-16"
            variants={itemVariants}
          >
            <div className="decorative-dash mx-auto mb-6"></div>
            <h2 className="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Get In Touch
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Whether you're a proud alumnus, a current supporter, or someone interested in joining our community, 
              we're here to connect and celebrate our shared legacy.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-5 gap-12">
            {/* Contact Info */}
            <motion.div 
              className="lg:col-span-2"
              variants={itemVariants}
            >
              <div className="glass-card p-8 mb-8">
                <div className="flex items-center mb-6">
                  <div className="w-3 h-8 bg-gradient-to-b from-crimson-600 to-crimson-800 rounded-full mr-4"></div>
                  <h2 className="font-display text-3xl font-bold text-gray-900">Contact Information</h2>
                </div>
                
                <div className="space-y-8">
                  <motion.div 
                    className="group flex items-start hover:bg-crimson-50/50 p-4 rounded-xl transition-all duration-300 cursor-pointer"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="bg-gradient-to-br from-crimson-600 to-crimson-800 w-14 h-14 rounded-2xl flex items-center justify-center flex-shrink-0 mr-5 shadow-crimson group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                      <FaPhone className="text-white text-xl" />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 mb-2 text-lg group-hover:text-crimson-700 transition-colors">Phone</h3>
                      <p className="text-gray-700">
                        <a href="tel:+15612310514" className="hover:text-crimson-700 transition-colors font-medium">(*************</a>
                      </p>
                    </div>
                  </motion.div>
                  
                  <motion.div 
                    className="group flex items-start hover:bg-crimson-50/50 p-4 rounded-xl transition-all duration-300 cursor-pointer"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="bg-gradient-to-br from-crimson-600 to-crimson-800 w-14 h-14 rounded-2xl flex items-center justify-center flex-shrink-0 mr-5 shadow-crimson group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                      <FaEnvelope className="text-white text-xl" />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 mb-2 text-lg group-hover:text-crimson-700 transition-colors">Email</h3>
                      <p className="text-gray-700">
                        <a href="mailto:<EMAIL>" className="hover:text-crimson-700 transition-colors font-medium"><EMAIL></a>
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>

              {/* Office Hours Card */}
              <motion.div 
                className="glass-card p-6 mb-8"
                variants={itemVariants}
              >
                <div className="flex items-center mb-4">
                  <FaClock className="text-crimson-600 text-xl mr-3" />
                  <h3 className="font-bold text-gray-900 text-lg">Office Hours</h3>
                </div>
                <div className="space-y-2 text-gray-700">
                  <p><span className="font-medium">Monday - Friday:</span> 9:00 AM - 5:00 PM</p>
                  <p><span className="font-medium">Saturday:</span> 10:00 AM - 2:00 PM</p>
                  <p><span className="font-medium">Sunday:</span> Closed</p>
                </div>
              </motion.div>
              
              {/* Social Media */}
              <motion.div 
                className="glass-card p-6"
                variants={itemVariants}
              >
                <div className="flex items-center mb-6">
                  <FaHeart className="text-crimson-600 text-xl mr-3" />
                  <h3 className="font-bold text-gray-900 text-lg">Follow Our Journey</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <motion.a 
                    href="https://www.facebook.com/share/1CoxTwQzMx/?mibextid=wwXIfr" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="group bg-gradient-to-br from-blue-600 to-blue-700 text-white p-4 rounded-2xl flex items-center justify-center hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.05, rotateY: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaFacebookF className="text-2xl group-hover:scale-110 transition-transform" />
                  </motion.a>
                  <motion.a 
                    href="https://twitter.com" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="group bg-gradient-to-br from-sky-500 to-sky-600 text-white p-4 rounded-2xl flex items-center justify-center hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.05, rotateY: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaTwitter className="text-2xl group-hover:scale-110 transition-transform" />
                  </motion.a>
                  <motion.a 
                    href="https://instagram.com" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="group bg-gradient-to-br from-pink-500 to-purple-600 text-white p-4 rounded-2xl flex items-center justify-center hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.05, rotateY: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaInstagram className="text-2xl group-hover:scale-110 transition-transform" />
                  </motion.a>
                  <motion.a 
                    href="https://linkedin.com" 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="group bg-gradient-to-br from-blue-700 to-blue-800 text-white p-4 rounded-2xl flex items-center justify-center hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.05, rotateY: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <FaLinkedinIn className="text-2xl group-hover:scale-110 transition-transform" />
                  </motion.a>
                </div>
              </motion.div>
            </motion.div>
            
            {/* Contact Form */}
            <motion.div 
              className="lg:col-span-3"
              variants={itemVariants}
            >
              <div className="glass-card p-10 relative overflow-hidden">
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-crimson-100/50 to-transparent rounded-full -translate-y-20 translate-x-20"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-gold-100/40 to-transparent rounded-full translate-y-16 -translate-x-16"></div>
                
                <div className="relative z-10">
                  <div className="flex items-center mb-8">
                    <div className="w-3 h-8 bg-gradient-to-b from-crimson-600 to-crimson-800 rounded-full mr-4"></div>
                    <h2 className="font-display text-3xl font-bold text-gray-900">Send Us a Message</h2>
                  </div>
                  
                  <p className="text-gray-600 mb-8 text-lg leading-relaxed">
                    Ready to reconnect with your alma mater? Share your thoughts, memories, or questions with us. 
                    We're always excited to hear from our alumni family!
                  </p>
                  
                  <form className="space-y-8">
                    <div className="grid md:grid-cols-2 gap-6">
                      <motion.div
                        whileFocus={{ scale: 1.02 }}
                        className="group"
                      >
                        <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                          Your Name*
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            id="name"
                            className="w-full px-5 py-4 rounded-2xl border-2 border-gray-200 shadow-sm
                                     focus:ring-4 focus:ring-crimson-700/20 focus:border-crimson-600
                                     transition-all duration-300 hover:border-gray-300
                                     bg-white/70 backdrop-blur-sm group-focus-within:bg-white"
                            placeholder="Enter your name"
                            required
                          />
                        </div>
                      </motion.div>
                      <motion.div
                        whileFocus={{ scale: 1.02 }}
                        className="group"
                      >
                        <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-2">
                          Email Address*
                        </label>
                        <div className="relative">
                          <input
                            type="email"
                            id="email"
                            className="w-full px-5 py-4 rounded-2xl border-2 border-gray-200 shadow-sm
                                     focus:ring-4 focus:ring-crimson-700/20 focus:border-crimson-600
                                     transition-all duration-300 hover:border-gray-300
                                     bg-white/70 backdrop-blur-sm group-focus-within:bg-white"
                            placeholder="Enter your email"
                            required
                          />
                        </div>
                      </motion.div>
                    </div>
                    
                    <motion.div
                      whileFocus={{ scale: 1.02 }}
                      className="group"
                    >
                      <label htmlFor="graduation" className="block text-sm font-semibold text-gray-700 mb-2">
                        Graduation Year (if applicable)
                      </label>
                      <input
                        type="text"
                        id="graduation"
                        className="w-full px-5 py-4 rounded-2xl border-2 border-gray-200 shadow-sm
                                 focus:ring-4 focus:ring-crimson-700/20 focus:border-crimson-600
                                 transition-all duration-300 hover:border-gray-300
                                 bg-white/70 backdrop-blur-sm group-focus-within:bg-white"
                        placeholder="e.g. 1995"
                      />
                    </motion.div>
                    
                    <motion.div
                      whileFocus={{ scale: 1.02 }}
                      className="group"
                    >
                      <label htmlFor="subject" className="block text-sm font-semibold text-gray-700 mb-2">
                        Subject*
                      </label>
                      <input
                        type="text"
                        id="subject"
                        className="w-full px-5 py-4 rounded-2xl border-2 border-gray-200 shadow-sm
                                 focus:ring-4 focus:ring-crimson-700/20 focus:border-crimson-600
                                 transition-all duration-300 hover:border-gray-300
                                 bg-white/70 backdrop-blur-sm group-focus-within:bg-white"
                        placeholder="What is this regarding?"
                        required
                      />
                    </motion.div>
                    
                    <motion.div
                      whileFocus={{ scale: 1.02 }}
                      className="group"
                    >
                      <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-2">
                        Message*
                      </label>
                      <textarea
                        id="message"
                        rows={6}
                        className="w-full px-5 py-4 rounded-2xl border-2 border-gray-200 shadow-sm
                                 focus:ring-4 focus:ring-crimson-700/20 focus:border-crimson-600
                                 transition-all duration-300 hover:border-gray-300 resize-none
                                 bg-white/70 backdrop-blur-sm group-focus-within:bg-white"
                        placeholder="How can we help you? Share your thoughts, memories, or questions..."
                        required
                      ></textarea>
                    </motion.div>
                    
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <button
                        type="submit"
                        className="group bg-gradient-to-r from-crimson-700 to-crimson-800 text-white 
                                 px-10 py-4 rounded-2xl font-semibold text-lg shadow-crimson 
                                 hover:shadow-xl transition-all duration-300 
                                 hover:from-crimson-800 hover:to-crimson-900
                                 flex items-center justify-center space-x-3 w-full md:w-auto"
                      >
                        <span>Send Message</span>
                        <FaPaperPlane className="group-hover:translate-x-1 group-hover:-translate-y-1 transition-transform duration-300" />
                      </button>
                    </motion.div>
                  </form>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Call to Action Section */}
          <motion.div 
            className="mt-20 text-center"
            variants={itemVariants}
          >
            <div className="glass-card p-12 bg-gradient-to-r from-crimson-50/80 to-beige-50/80">
              <h3 className="font-display text-3xl font-bold text-gray-900 mb-4">
                Join Our Alumni Network
              </h3>
              <p className="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
                Stay connected with your fellow alumni, receive updates about events, 
                and be part of the ongoing legacy of Palm Beach Lakes High School.
              </p>
              <motion.a
                href="/join"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-gold-500 to-gold-600 
                         text-crimson-900 rounded-2xl font-semibold text-lg shadow-lg 
                         hover:shadow-xl transition-all duration-300 space-x-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span>Become a Member</span>
                <FaHeart className="text-red-600" />
              </motion.a>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default Contact; 