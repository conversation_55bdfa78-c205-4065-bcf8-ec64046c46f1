import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaEdit, FaTrash, FaLock } from 'react-icons/fa';
import { supabase } from '../lib/supabase';
import { useAuth } from '../hooks/useAuth';
import type { Database } from '../lib/database.types';
import ProfileEditor from '../components/directory/ProfileEditor';
import { motion } from 'framer-motion';
import PageLayout from '../components/layout/PageLayout';

type Member = Database['public']['Tables']['members']['Row'];

const MemberProfile = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [member, setMember] = useState<Member | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [password, setPassword] = useState('');
  const [editedMember, setEditedMember] = useState<Partial<Member>>({});

  // Check if the current user is the owner of this profile
  const isOwner = user && user.email === member?.email;

  useEffect(() => {
    let isSubscribed = true;

    const fetchMember = async () => {
      if (!id) {
        setError('No member ID provided');
        setLoading(false);
        return;
      }

      try {
        const { data, error: fetchError } = await supabase
          .from('members')
          .select('*')
          .eq('id', id)
          .single();
          
        if (fetchError) {
          console.error('Error fetching member:', fetchError);
          throw new Error('Member not found');
        }
        
        if (isSubscribed && data) {
          setMember(data);
        }
      } catch (err: any) {
        if (isSubscribed) {
          setError(err.message || 'Failed to fetch member profile');
          console.error("Error fetching member profile:", err);
        }
      } finally {
        if (isSubscribed) {
          setLoading(false);
        }
      }
    };

    fetchMember();

    return () => {
      isSubscribed = false;
    };
  }, [id]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleSave = async (profileData: Partial<Member>, imageFile?: File) => {
    try {
      // Handle image upload if provided
      if (imageFile) {
        const formData = new FormData();
        formData.append('avatar', imageFile);
        // Handle image upload logic here
      }

      // Update member data
      const { error: updateError } = await supabase
        .from('members')
        .update(profileData)
        .eq('id', id);

      if (updateError) throw updateError;

      // Refresh member data
      setMember({ ...member!, ...profileData } as Member);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving profile:', error);
      setError('Failed to save profile');
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete your profile? This action cannot be undone.')) {
      return;
    }

    try {
      setIsDeleting(true);
      const { error: deleteError } = await supabase
        .from('members')
        .delete()
        .eq('id', id!);
        
      if (deleteError) throw deleteError;
      
      navigate('/directory', { replace: true });
    } catch (err: any) {
      setError(err.message || 'Failed to delete profile');
      console.error("Profile deletion error:", err);
      setIsDeleting(false);
    }
  };

  // Handle password verification
  const handlePasswordVerify = async () => {
    try {
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email: member?.email || '',
        password: password
      });

      if (signInError) {
        console.error('Sign in error:', signInError);
        setError('Invalid password. Please try again.');
        return;
      }

      if (data.user?.id === id) {
        setIsEditing(true);
        setShowPasswordModal(false);
        setPassword('');
        setError(null);
      } else {
        setError('Invalid password. Please try again.');
      }
    } catch (err) {
      console.error('Password verification error:', err);
      setError('An error occurred while verifying your password. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-beige-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-crimson-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg text-crimson-700">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error || !member) {
    return (
      <div className="min-h-screen bg-beige-50 flex items-center justify-center p-4">
        <div className="text-center max-w-md">
          <p className="text-xl text-crimson-700 font-bold mb-2">Error Loading Profile</p>
          <p className="text-gray-600 mb-6">{error || 'Member not found'}</p>
          <Link 
            to="/directory"
            className="inline-flex items-center text-crimson-600 hover:text-crimson-700 font-medium"
          >
            <FaArrowLeft className="w-4 h-4 mr-2" />
            Back to Directory
          </Link>
        </div>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div className="min-h-screen bg-beige-50 pt-20 pb-12 px-4">
        <div className="max-w-3xl mx-auto">
          <ProfileEditor
            initialData={member}
            onSave={handleSave}
            onCancel={handleCancelEdit}
          />
        </div>
      </div>
    );
  }

  // Construct profile image URL
  const fallbackImageUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(member.name)}&background=E05D69&color=fff&size=128`;
  const displayImageUrl = member.avatar_url || fallbackImageUrl;

  return (
    <PageLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-4xl mx-auto"
      >
        <Link to="/directory" className="inline-flex items-center text-[#7D0E17] mb-6">
          ← Back to Directory
        </Link>

        <div className="flex justify-between items-center mb-8">
          {isOwner && (
            <div className="flex space-x-4">
              <button
                onClick={handleEdit}
                className="inline-flex items-center px-4 py-2 bg-crimson-600 text-white rounded-md hover:bg-crimson-700 transition-colors"
                disabled={isDeleting}
              >
                <FaEdit className="w-4 h-4 mr-2" />
                Edit Profile
              </button>
              <button
                onClick={handleDelete}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                disabled={isDeleting}
              >
                <FaTrash className="w-4 h-4 mr-2" />
                {isDeleting ? 'Deleting...' : 'Delete Profile'}
              </button>
            </div>
          )}
        </div>

        <div className="bg-white rounded-t-2xl p-12 relative">
          {!isEditing && (
            <button
              onClick={() => setShowPasswordModal(true)}
              className="absolute top-4 right-4 flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg hover:bg-white/20 transition-colors"
            >
              <FaLock className="w-4 h-4" />
              <span>Password</span>
            </button>
          )}
          
          {isEditing && (
            <div className="absolute top-4 right-4 flex gap-2">
              <button
                onClick={async () => {
                  try {
                    const { error: updateError } = await supabase
                      .from('members')
                      .update(editedMember)
                      .eq('id', id);

                    if (updateError) throw updateError;

                    setMember({ ...member!, ...editedMember } as Member);
                    setIsEditing(false);
                    setEditedMember({});
                  } catch (err) {
                    console.error('Update error:', err);
                    setError('Failed to update profile');
                  }
                }}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Save Changes
              </button>
              <button
                onClick={handleDelete}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete Profile
              </button>
            </div>
          )}

          <div className="flex items-center gap-8">
            <img 
              src={displayImageUrl}
              alt={member.name} 
              className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg mb-6"
            />
            <div>
              {isEditing ? (
                <input
                  type="text"
                  value={editedMember.name || member.name}
                  onChange={(e) => setEditedMember({ ...editedMember, name: e.target.value })}
                  className="text-4xl font-bold bg-transparent border-b border-white/30 focus:border-white outline-none"
                />
              ) : (
                <h1 className="text-4xl font-bold">{member.name}</h1>
              )}
              <p className="text-xl opacity-80">Class of {member.graduation_year}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-b-2xl p-8 shadow-xl space-y-8">
          <div>
            <h2 className="text-2xl font-bold mb-4">Contact Information</h2>
            <div className="space-y-4">
              <div>
                <label className="text-sm text-gray-500">Email</label>
                <p>{member.email}</p>
              </div>
              {isEditing ? (
                <div>
                  <label className="text-sm text-gray-500">LinkedIn</label>
                  <input
                    type="url"
                    value={editedMember.linkedin || member.linkedin || ''}
                    onChange={(e) => setEditedMember({ ...editedMember, linkedin: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    placeholder="LinkedIn URL"
                  />
                </div>
              ) : member.linkedin && (
                <div>
                  <label className="text-sm text-gray-500">LinkedIn</label>
                  <a href={member.linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {member.linkedin}
                  </a>
                </div>
              )}
            </div>
          </div>

          <div>
            <h2 className="text-2xl font-bold mb-4">About</h2>
            {isEditing ? (
              <textarea
                value={editedMember.bio || member.bio || ''}
                onChange={(e) => setEditedMember({ ...editedMember, bio: e.target.value })}
                className="w-full px-3 py-2 border rounded-lg"
                rows={4}
                placeholder="Tell us about yourself"
              />
            ) : (
              <p>{member.bio || 'No bio provided'}</p>
            )}
          </div>

          {isEditing && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Professional Information</h2>
              <div className="space-y-4">
                <div>
                  <label className="text-sm text-gray-500">Profession</label>
                  <input
                    type="text"
                    value={editedMember.profession || member.profession || ''}
                    onChange={(e) => setEditedMember({ ...editedMember, profession: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    placeholder="Your profession"
                  />
                </div>
                <div>
                  <label className="text-sm text-gray-500">Location</label>
                  <input
                    type="text"
                    value={editedMember.location || member.location || ''}
                    onChange={(e) => setEditedMember({ ...editedMember, location: e.target.value })}
                    className="w-full px-3 py-2 border rounded-lg"
                    placeholder="Your location"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {showPasswordModal && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
              <h2 className="text-2xl font-bold mb-4">Enter Password</h2>
              <p className="text-gray-600 mb-6">
                Please enter your password to edit your profile.
              </p>
              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
                  <p className="text-sm">{error}</p>
                </div>
              )}
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-2 border rounded-lg mb-4"
                placeholder="Enter your password"
              />
              <div className="flex justify-end gap-4">
                <button
                  onClick={() => {
                    setShowPasswordModal(false);
                    setPassword('');
                    setError(null);
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePasswordVerify}
                  className="px-4 py-2 bg-[#7D0E17] text-white rounded-lg hover:bg-[#6B0C14] transition-colors"
                >
                  Verify
                </button>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </PageLayout>
  );
};

export default MemberProfile; 