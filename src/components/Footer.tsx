import { Link } from 'react-router-dom';
import { <PERSON>aFace<PERSON><PERSON>, F<PERSON><PERSON><PERSON><PERSON>, FaInstagram, FaLinkedinIn } from 'react-icons/fa';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#3D0A0F] text-white">
      {/* Quote Section */}
      <div className="bg-[#7D0E17] border-b border-white/10">
        <div className="container mx-auto px-4 max-w-6xl py-16 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Decorative Line */}
            <div className="w-16 h-1 bg-[#F4B223] mx-auto mb-8"></div>
            <blockquote className="text-[2.5rem] leading-[1.2] font-['Fraunces'] mb-4 font-light">
              "Our greatest responsibility is to be good ancestors<br/>to the generations that follow us."
            </blockquote>
            <cite className="text-white/80 text-lg font-['Inter']">— Alumni Legacy Statement</cite>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 max-w-6xl py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Organization Info */}
          <div className="col-span-1">
            <div className="flex items-start gap-3 mb-4">
              <img 
                src="/images/logo/pblnaa-logo.png" 
                alt="PBLNAA Logo" 
                className="w-12 h-12" 
              />
              <div>
                <h3 className="text-xl font-['Fraunces'] font-semibold">Palm Beach Lakes</h3>
                <h4 className="text-lg font-['Inter'] text-white/90">National Alumni Association</h4>
              </div>
            </div>
            <p className="text-white/80 mb-6 font-['Inter']">
              Building bridges between successful alumni and aspiring students through mentorship,
              scholarships, and lifelong connections.
            </p>
            <div className="flex gap-4">
              <a 
                href="https://www.facebook.com/share/1CoxTwQzMx/?mibextid=wwXIfr" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
              >
                <FaFacebookF className="text-white" />
              </a>
              <a 
                href="#" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
              >
                <FaTwitter className="text-white" />
              </a>
              <a 
                href="#" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
              >
                <FaInstagram className="text-white" />
              </a>
              <a 
                href="#" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-colors"
              >
                <FaLinkedinIn className="text-white" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-xl font-['Fraunces'] font-semibold mb-6">Quick Links</h3>
            <ul className="space-y-3 font-['Inter']">
              <li>
                <Link to="/about" className="text-white/80 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/events" className="text-white/80 hover:text-white transition-colors">
                  Events
                </Link>
              </li>
              <li>
                <Link to="/gallery" className="text-white/80 hover:text-white transition-colors">
                  Photo Gallery
                </Link>
              </li>
              <li>
                <Link to="/scholarships" className="text-white/80 hover:text-white transition-colors">
                  Scholarships
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-white/80 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Get Involved */}
          <div className="col-span-1">
            <h3 className="text-xl font-['Fraunces'] font-semibold mb-6">Get Involved</h3>
            <ul className="space-y-3 font-['Inter']">
              <li>
                <Link to="/join" className="text-white/80 hover:text-white transition-colors">
                  Become a Member
                </Link>
              </li>
              <li>
                <Link to="/volunteer" className="text-white/80 hover:text-white transition-colors">
                  Volunteer
                </Link>
              </li>
              <li>
                <Link to="/mentor" className="text-white/80 hover:text-white transition-colors">
                  Mentor a Student
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Us */}
          <div className="col-span-1">
            <h3 className="text-xl font-['Fraunces'] font-semibold mb-6">Contact Us</h3>
            <ul className="space-y-3 font-['Inter']">
              <li>
                <a href="tel:+15612310514" className="text-white/80 hover:text-white transition-colors">
                  (*************
                </a>
              </li>
              <li>
                <a href="mailto:<EMAIL>" className="text-white/80 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </li>
            </ul>
            <div className="mt-6 p-4 bg-white/5 rounded-lg">
              <p className="text-sm mb-3 font-['Inter']">
                Looking to connect with fellow alumni? Join our alumni directory today!
              </p>
              <Link 
                to="/directory" 
                className="text-[#F4B223] hover:text-[#F4B223]/80 transition-colors inline-flex items-center font-['Inter']"
              >
                Explore Alumni Directory →
              </Link>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="mt-16 pt-8 border-t border-white/10">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-white/60 text-sm font-['Inter']">
              © {currentYear} Palm Beach Lakes National Alumni Association. All rights reserved.
            </p>
            <div className="flex gap-6">
              <Link to="/privacy" className="text-white/60 text-sm hover:text-white transition-colors font-['Inter']">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-white/60 text-sm hover:text-white transition-colors font-['Inter']">
                Terms of Service
              </Link>
              <Link to="/accessibility" className="text-white/60 text-sm hover:text-white transition-colors font-['Inter']">
                Accessibility
              </Link>
              <Link to="/sitemap" className="text-white/60 text-sm hover:text-white transition-colors font-['Inter']">
                Sitemap
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 