{"name": "pbl-alumni", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite preview --port=$PORT --host=0.0.0.0", "add-gallery-photos": "node scripts/add-gallery-photos.js"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@supabase/supabase-js": "^2.49.8", "autoprefixer": "^10.4.17", "framer-motion": "^11.18.2", "postcss": "^8.4.35", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.0", "react-icons": "^5.5.0", "react-router-dom": "^6.30.0", "tailwindcss": "^3.4.1", "dotenv": "^16.4.5"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.3.3", "typescript-eslint": "^8.33.0", "vite": "^5.1.4"}}