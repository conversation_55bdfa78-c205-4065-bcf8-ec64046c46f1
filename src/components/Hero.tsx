import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <div className="relative min-h-screen w-full flex flex-col items-center justify-center overflow-hidden">
      {/* Graduation Photo Background with Dark Overlay */}
      <img 
        src="/images/graduation-photo.png"
        alt="Graduation Silhouette Background" 
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{objectPosition: 'center'}}
      />
      <div className="absolute inset-0 w-full h-full bg-black/70 z-10" /> {/* Dark overlay for photo subtlety */}

      {/* Main Content Area with Maroon Background */}
      <div className="relative z-20 w-full min-h-screen bg-[#7D0E17] flex flex-col items-center justify-center py-20 px-4">
        {/* Logo Circle */}
        <div className="w-36 h-36 bg-white rounded-full flex items-center justify-center mb-12 shadow-lg">
          <img src="/images/logo/pblnaa-logo.png" alt="PBL Ram Logo" className="w-28 h-28" />
        </div>
        
        {/* Heading */}
        <h1 className="text-white text-5xl md:text-6xl font-bold text-center mb-2 leading-tight">
          Class of 2026
        </h1>
        <h2 className="text-white text-3xl md:text-5xl font-bold text-center mb-10 leading-tight">
          Donate
        </h2>
        
        {/* Description */}
        <p className="text-white text-center max-w-2xl mb-10 text-lg md:text-xl">
          Building bridges between successful alumni and aspiring students
          through mentorship, scholarships, and lifelong connections
        </p>
        
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-5">
          <Link to="/support">
            <button className="px-6 py-3 bg-white text-[#7D0E17] font-semibold rounded-md flex items-center shadow-md hover:bg-gray-100 transition-colors">
              Support Future Rams
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-5 h-5 ml-2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Hero; 