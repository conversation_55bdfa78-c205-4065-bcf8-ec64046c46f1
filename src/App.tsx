import { createBrowserRouter, RouterProvider, useLocation, Outlet } from 'react-router-dom';
import { useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import './App.css';
import './components/layout/backgroundStyles.css';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Events from './pages/Events';
import Gallery from './pages/Gallery';
import Contact from './pages/Contact';
import Join from './pages/Join';
import JoinSuccess from './pages/JoinSuccess';
import Directory from './pages/Directory';
import MemberProfile from './pages/MemberProfile';
import Dashboard from './pages/admin/Dashboard';

// Scroll to top on navigation
const ScrollToTop = () => {
  const { pathname } = useLocation();
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);
  
  return null;
};

// Root layout component
const RootLayout = () => {
  return (
    <div className="flex flex-col min-h-screen font-sans school-bg">
      {/* Decorative background elements */}
      <div className="decorative-circle decorative-circle-1"></div>
      <div className="decorative-circle decorative-circle-2"></div>
      
      <Navbar />
      <main className="pt-[72px] flex-grow w-full relative z-10">
        <Outlet />
      </main>
      <Footer />
    </div>
  );
};

// Page transitions wrapper
const PageTransitionLayout = () => {
  const location = useLocation();
  
  return (
    <AnimatePresence mode="wait">
      <Outlet key={location.pathname} />
    </AnimatePresence>
  );
};

// Router configuration
const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <>
        <ScrollToTop />
        <RootLayout />
      </>
    ),
    children: [
      {
        element: <PageTransitionLayout />,
        children: [
          { index: true, element: <Home /> },
          { path: 'about', element: <About /> },
          { path: 'events', element: <Events /> },
          { path: 'gallery', element: <Gallery /> },
          { path: 'contact', element: <Contact /> },
          { path: 'join', element: <Join /> },
          { path: 'join/success', element: <JoinSuccess /> },
          { path: 'directory', element: <Directory /> },
          { path: 'directory/:id', element: <MemberProfile /> }
        ]
      }
    ]
  },
  // Admin routes - separate layout without navbar/footer
  {
    path: '/admin/*',
    element: (
      <>
        <ScrollToTop />
        <Dashboard />
      </>
    )
  }
]);

function App() {
  return <RouterProvider router={router} />;
}

export default App;
