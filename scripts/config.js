import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
config({ path: join(__dirname, '..', '.env') });

export const supabaseUrl = process.env.VITE_SUPABASE_URL;
export const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; 